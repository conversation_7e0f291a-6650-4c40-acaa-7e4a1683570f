#!/bin/bash

# 测试中断功能的脚本

echo "测试中断处理功能..."
echo "启动打印脚本，3秒后发送中断信号..."

# 在后台启动打印脚本
echo "y" | timeout 10 ./all-print.sh &
PRINT_PID=$!

# 等待3秒
sleep 3

# 发送中断信号
echo "发送中断信号 (SIGINT)..."
kill -INT $PRINT_PID

# 等待进程结束
wait $PRINT_PID
EXIT_CODE=$?

echo "进程退出码: $EXIT_CODE"

# 检查是否生成了日志文件
echo "检查日志文件..."
LATEST_LOG=$(ls -t print_log_*.txt 2>/dev/null | head -n1)

if [ -n "$LATEST_LOG" ]; then
    echo "找到日志文件: $LATEST_LOG"
    echo "日志内容:"
    cat "$LATEST_LOG"
    
    # 检查是否有中断记录
    if grep -q "INTERRUPTED" "$LATEST_LOG"; then
        echo "✓ 中断状态已正确记录"
    else
        echo "⚠ 未找到中断记录"
    fi
else
    echo "⚠ 未找到日志文件"
fi

echo "中断测试完成"
