#!/bin/bash

# 测试打印脚本的双面打印检测功能

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

echo "========================================"
echo "    测试双面打印检测功能"
echo "========================================"
echo

# 设置打印机名称
PRINTER_NAME="SHARP-MX-3618N"

print_info "测试打印机: $PRINTER_NAME"

# 模拟 all-print.sh 中的双面打印检测逻辑
print_info "检查打印机双面打印支持..."

# 获取打印机选项
printer_options=$(lpoptions -p "$PRINTER_NAME" -l 2>/dev/null)

# 检查是否支持双面打印 (sides 或 Duplex)
if echo "$printer_options" | grep -q -E "(sides|Duplex)"; then
    print_success "✓ 打印机支持双面打印"
    
    # 检查 Duplex 选项
    if echo "$printer_options" | grep -q "Duplex"; then
        duplex_options=$(echo "$printer_options" | grep "Duplex" | head -n1)
        print_info "支持的选项: $duplex_options"
        
        if echo "$duplex_options" | grep -q "DuplexNoTumble"; then
            print_info "  ✓ 支持长边装订双面打印 (Duplex)"
        fi
        if echo "$duplex_options" | grep -q "DuplexTumble"; then
            print_info "  ✓ 支持短边装订双面打印 (Duplex)"
        fi
    fi
    
    # 检查 sides 选项
    if echo "$printer_options" | grep -q "sides"; then
        sides_options=$(echo "$printer_options" | grep "sides" | head -n1)
        print_info "支持的选项: $sides_options"
        
        if echo "$sides_options" | grep -q "two-sided-long-edge"; then
            print_info "  ✓ 支持长边装订双面打印 (sides)"
        fi
        if echo "$sides_options" | grep -q "two-sided-short-edge"; then
            print_info "  ✓ 支持短边装订双面打印 (sides)"
        fi
    fi
else
    print_warning "⚠ 打印机不支持双面打印，将使用单面打印"
fi

echo
print_info "测试双面打印选项设置..."

# 模拟打印选项设置逻辑
print_options=""
duplex_support=false

# 检查打印机支持的双面打印选项
if echo "$printer_options" | grep -q -E "(sides|Duplex)"; then
    duplex_support=true
    
    # 优先使用 Duplex 选项 (适用于商用打印机如SHARP)
    if echo "$printer_options" | grep -q "Duplex"; then
        if echo "$printer_options" | grep "Duplex" | grep -q "DuplexNoTumble"; then
            print_options="-o Duplex=DuplexNoTumble"
            print_info "✓ 设置双面打印选项: $print_options (长边装订)"
        elif echo "$printer_options" | grep "Duplex" | grep -q "DuplexTumble"; then
            print_options="-o Duplex=DuplexTumble"
            print_info "✓ 设置双面打印选项: $print_options (短边装订)"
        else
            print_options="-o Duplex=DuplexNoTumble"
            print_info "✓ 设置默认双面打印选项: $print_options"
        fi
    # 备用：使用 sides 选项
    elif echo "$printer_options" | grep -q "sides"; then
        if echo "$printer_options" | grep "sides" | grep -q "two-sided-long-edge"; then
            print_options="-o sides=two-sided-long-edge"
            print_info "✓ 设置双面打印选项: $print_options (长边装订)"
        elif echo "$printer_options" | grep "sides" | grep -q "two-sided-short-edge"; then
            print_options="-o sides=two-sided-short-edge"
            print_info "✓ 设置双面打印选项: $print_options (短边装订)"
        else
            print_options="-o sides=two-sided-long-edge"
            print_info "✓ 设置默认双面打印选项: $print_options"
        fi
    fi
else
    print_warning "打印机不支持双面打印，使用单面打印"
    # 尝试设置单面打印
    if echo "$printer_options" | grep -q "Duplex"; then
        print_options="-o Duplex=None"
    elif echo "$printer_options" | grep -q "sides"; then
        print_options="-o sides=one-sided"
    fi
    print_info "设置单面打印选项: $print_options"
fi

echo
echo "========================================"
print_success "测试完成！"
if [ "$duplex_support" = true ]; then
    print_success "双面打印功能已正确配置"
    print_info "推荐打印选项: $print_options"
else
    print_warning "双面打印功能不可用"
fi
echo "========================================"
