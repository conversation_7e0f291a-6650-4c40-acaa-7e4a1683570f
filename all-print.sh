#!/bin/bash

# Word文件批量双面打印脚本
# 使用方法: ./all-print.sh [目录路径] [打印机名称] [选项]
# 选项:
#   --resume-from-log <日志文件>  从指定日志文件恢复打印失败的文件
#   --retry-failed               重新打印所有失败的文件

# 配置部分
DIRECTORY=""
PRINTER_NAME=""
RESUME_LOG_FILE=""
RETRY_FAILED=false

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --resume-from-log)
                RESUME_LOG_FILE="$2"
                shift 2
                ;;
            --retry-failed)
                RETRY_FAILED=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                if [ -z "$DIRECTORY" ]; then
                    DIRECTORY="$1"
                elif [ -z "$PRINTER_NAME" ]; then
                    PRINTER_NAME="$1"
                else
                    print_error "未知参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # 设置默认值
    DIRECTORY="${DIRECTORY:-$(pwd)}"
}

# 显示帮助信息
show_help() {
    echo "Word文件批量双面打印工具"
    echo
    echo "使用方法:"
    echo "  $0 [目录路径] [打印机名称] [选项]"
    echo
    echo "参数:"
    echo "  目录路径          要打印的Word文件所在目录 (默认: 当前目录)"
    echo "  打印机名称        指定打印机 (默认: 自动检测)"
    echo
    echo "选项:"
    echo "  --resume-from-log <日志文件>  从指定日志文件恢复打印失败的文件"
    echo "  --retry-failed               重新打印最近日志中失败的文件"
    echo "  --help, -h                   显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0                           # 打印当前目录的所有Word文件"
    echo "  $0 /path/to/docs             # 打印指定目录的Word文件"
    echo "  $0 . MyPrinter               # 使用指定打印机"
    echo "  $0 --retry-failed            # 重新打印失败的文件"
    echo "  $0 --resume-from-log print_log_20250818_143022.txt"
}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色信息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 全局变量
PRINT_LOG_FILE=""
INTERRUPTED=false
CURRENT_FILE=""

# 清理函数
cleanup() {
    # 清理所有临时PDF文件
    rm -f /tmp/print_*.pdf 2>/dev/null

    # 如果被中断，记录状态
    if [ "$INTERRUPTED" = true ] && [ -n "$CURRENT_FILE" ]; then
        echo "INTERRUPTED:$(basename "$CURRENT_FILE")" >> "$PRINT_LOG_FILE"
        print_warning "打印被用户中断"
        print_info "打印日志已保存到: $PRINT_LOG_FILE"
    fi
}

# 中断处理函数
handle_interrupt() {
    INTERRUPTED=true
    print_warning "收到中断信号，正在安全退出..."

    # 如果有正在处理的文件，标记为中断
    if [ -n "$CURRENT_FILE" ]; then
        print_info "当前文件 $(basename "$CURRENT_FILE") 的打印可能未完成"
    fi

    cleanup
    exit 130
}

# 设置信号处理
trap handle_interrupt INT TERM
trap cleanup EXIT

# 检查LibreOffice是否安装
check_libreoffice() {
    if ! command -v libreoffice &> /dev/null; then
        print_error "LibreOffice未安装！"
        echo "请运行以下命令安装："
        echo "sudo pacman -S libreoffice-fresh"
        exit 1
    fi
    print_success "LibreOffice已安装"
}

# 检查CUPS服务
check_cups() {
    if ! systemctl is-active --quiet cups; then
        print_warning "CUPS服务未运行，正在启动..."
        sudo systemctl start cups
        if ! systemctl is-active --quiet cups; then
            print_error "无法启动CUPS服务"
            exit 1
        fi
    fi
    print_success "CUPS服务正常运行"
}

# 获取默认打印机或让用户选择
get_printer() {
    if [ -n "$PRINTER_NAME" ]; then
        # 检查指定的打印机是否存在
        if lpstat -p "$PRINTER_NAME" &>/dev/null; then
            print_success "使用指定打印机: $PRINTER_NAME"
            return 0
        else
            print_error "打印机 '$PRINTER_NAME' 不存在"
            exit 1
        fi
    fi
    
    # 尝试获取默认打印机
    DEFAULT_PRINTER=$(lpstat -d 2>/dev/null | grep "default destination" | awk '{print $NF}')
    
    if [ -n "$DEFAULT_PRINTER" ]; then
        PRINTER_NAME="$DEFAULT_PRINTER"
        print_success "使用默认打印机: $PRINTER_NAME"
        return 0
    fi
    
    # 列出所有可用打印机
    print_info "可用的打印机："
    lpstat -p | awk '{print "  " $2}'
    
    # 获取第一个可用打印机
    FIRST_PRINTER=$(lpstat -p | head -n1 | awk '{print $2}')
    if [ -n "$FIRST_PRINTER" ]; then
        PRINTER_NAME="$FIRST_PRINTER"
        print_warning "未设置默认打印机，使用: $PRINTER_NAME"
        read -p "是否继续? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_error "未找到可用的打印机"
        exit 1
    fi
}

# 检查打印机是否支持双面打印
check_duplex_support() {
    print_info "检查打印机双面打印支持..."

    # 获取打印机选项
    local printer_options=$(lpoptions -p "$PRINTER_NAME" -l 2>/dev/null)

    # 检查是否支持双面打印 (sides 或 Duplex)
    if echo "$printer_options" | grep -q -E "(sides|Duplex)"; then
        print_success "✓ 打印机支持双面打印"

        # 检查 sides 选项
        if echo "$printer_options" | grep -q "sides"; then
            local sides_options=$(echo "$printer_options" | grep "sides" | head -n1)
            print_info "支持的选项: $sides_options"

            if echo "$sides_options" | grep -q "two-sided-long-edge"; then
                print_info "  ✓ 支持长边装订双面打印 (sides)"
            fi
            if echo "$sides_options" | grep -q "two-sided-short-edge"; then
                print_info "  ✓ 支持短边装订双面打印 (sides)"
            fi
        fi

        # 检查 Duplex 选项
        if echo "$printer_options" | grep -q "Duplex"; then
            local duplex_options=$(echo "$printer_options" | grep "Duplex" | head -n1)
            print_info "支持的选项: $duplex_options"

            if echo "$duplex_options" | grep -q "DuplexNoTumble"; then
                print_info "  ✓ 支持长边装订双面打印 (Duplex)"
            fi
            if echo "$duplex_options" | grep -q "DuplexTumble"; then
                print_info "  ✓ 支持短边装订双面打印 (Duplex)"
            fi
        fi

        return 0
    else
        print_warning "⚠ 打印机不支持双面打印，将使用单面打印"

        # 显示所有可用选项供调试
        if [ -n "$printer_options" ]; then
            print_info "打印机支持的选项:"
            echo "$printer_options" | while read -r line; do
                if [ -n "$line" ]; then
                    echo "    $line"
                fi
            done
        fi

        return 1
    fi
}

# 初始化打印日志
init_print_log() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    PRINT_LOG_FILE="$DIRECTORY/print_log_${timestamp}.txt"

    # 创建日志文件
    cat > "$PRINT_LOG_FILE" << EOF
# 打印日志文件
# 创建时间: $(date)
# 目标目录: $DIRECTORY
# 打印机: $PRINTER_NAME
# 格式: 状态:文件名:时间戳
# 状态: SUCCESS(成功), FAILED(失败), SKIPPED(跳过), INTERRUPTED(中断)

EOF

    print_info "打印日志文件: $PRINT_LOG_FILE"
}

# 记录打印状态
log_print_status() {
    local status="$1"
    local filename="$2"
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")

    echo "$status:$filename:$timestamp" >> "$PRINT_LOG_FILE"
}

# 显示打印日志摘要
show_print_summary() {
    if [ ! -f "$PRINT_LOG_FILE" ]; then
        return
    fi

    local total_files=$(grep -c "^[A-Z]" "$PRINT_LOG_FILE" 2>/dev/null || echo "0")
    local success_count=$(grep -c "^SUCCESS:" "$PRINT_LOG_FILE" 2>/dev/null || echo "0")
    local failed_count=$(grep -c "^FAILED:" "$PRINT_LOG_FILE" 2>/dev/null || echo "0")
    local skipped_count=$(grep -c "^SKIPPED:" "$PRINT_LOG_FILE" 2>/dev/null || echo "0")
    local interrupted_count=$(grep -c "^INTERRUPTED:" "$PRINT_LOG_FILE" 2>/dev/null || echo "0")

    echo
    print_info "=== 打印状态摘要 ==="
    print_success "成功: $success_count 个文件"
    if [ "$failed_count" -gt 0 ]; then
        print_error "失败: $failed_count 个文件"
    fi
    if [ "$skipped_count" -gt 0 ]; then
        print_warning "跳过: $skipped_count 个文件"
    fi
    if [ "$interrupted_count" -gt 0 ]; then
        print_warning "中断: $interrupted_count 个文件"
    fi

    echo
    print_info "详细日志请查看: $PRINT_LOG_FILE"

    # 显示失败和中断的文件
    if [ "$failed_count" -gt 0 ] || [ "$interrupted_count" -gt 0 ]; then
        echo
        print_warning "需要重新处理的文件:"
        grep -E "^(FAILED|INTERRUPTED):" "$PRINT_LOG_FILE" 2>/dev/null | while IFS=':' read -r status filename timestamp; do
            echo "  - $filename ($status)"
        done
    fi
}

# 检查文件是否已经打印过
is_file_already_printed() {
    local filename="$1"
    if [ -f "$PRINT_LOG_FILE" ]; then
        if grep -q "^SUCCESS:$(basename "$filename"):" "$PRINT_LOG_FILE"; then
            return 0  # 已打印
        fi
    fi
    return 1  # 未打印
}

# 从日志文件获取失败的文件列表
get_failed_files_from_log() {
    local log_file="$1"
    if [ ! -f "$log_file" ]; then
        print_error "日志文件不存在: $log_file"
        return 1
    fi

    # 提取失败和中断的文件
    grep -E "^(FAILED|INTERRUPTED):" "$log_file" | cut -d':' -f2 | sort -u
}

# 查找最新的日志文件
find_latest_log() {
    local latest_log=$(ls -t "$DIRECTORY"/print_log_*.txt 2>/dev/null | head -n1)
    if [ -n "$latest_log" ]; then
        echo "$latest_log"
        return 0
    fi
    return 1
}

# 从日志恢复打印
resume_from_log() {
    local log_file="$1"
    local failed_files

    print_info "从日志文件恢复: $log_file"

    # 获取失败的文件列表
    mapfile -t failed_files < <(get_failed_files_from_log "$log_file")

    if [ ${#failed_files[@]} -eq 0 ]; then
        print_success "日志中没有失败的文件需要重新打印"
        return 0
    fi

    print_info "找到 ${#failed_files[@]} 个需要重新打印的文件:"
    for file in "${failed_files[@]}"; do
        echo "  $file"
    done

    echo
    read -p "是否开始重新打印这些文件? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "取消重新打印"
        return 0
    fi

    # 构建完整文件路径并打印
    local success_count=0
    local fail_count=0
    local current_index=0
    local total_files=${#failed_files[@]}

    for filename in "${failed_files[@]}"; do
        ((current_index++))

        # 查找文件的完整路径
        local full_path=$(find "$DIRECTORY" -name "$filename" -type f | head -n1)

        if [ -z "$full_path" ]; then
            print_warning "文件未找到: $filename"
            log_print_status "FAILED" "$filename"
            ((fail_count++))
            continue
        fi

        print_info "重新打印进度: $current_index/$total_files - $filename"

        if print_word_file "$full_path"; then
            ((success_count++))
        else
            ((fail_count++))
        fi

        # 延迟
        if [ $current_index -lt $total_files ]; then
            sleep 2
        fi
    done

    echo
    print_success "重新打印完成！成功: $success_count, 失败: $fail_count"
}



# 打印Word文件
print_word_file() {
    local file="$1"
    local filename=$(basename "$file")

    # 设置当前处理的文件
    CURRENT_FILE="$file"

    print_info "正在打印: $filename"

    # 检查是否已经打印过
    if is_file_already_printed "$file"; then
        print_warning "文件 $filename 已经打印过，跳过"
        log_print_status "SKIPPED" "$filename"
        CURRENT_FILE=""
        return 0
    fi

    # 检查文件是否存在
    if [ ! -f "$file" ]; then
        print_error "文件不存在: $file"
        return 1
    fi

    # 检查打印机状态
    if ! lpstat -p "$PRINTER_NAME" | grep -q "enabled"; then
        print_error "打印机 $PRINTER_NAME 未启用"
        return 1
    fi

    # 检查打印机是否支持双面打印并设置选项
    local print_options=""
    local duplex_support=false
    local printer_options=$(lpoptions -p "$PRINTER_NAME" -l 2>/dev/null)

    # 检查打印机支持的双面打印选项
    if echo "$printer_options" | grep -q -E "(sides|Duplex)"; then
        duplex_support=true

        # 优先使用 Duplex 选项 (适用于商用打印机如SHARP)
        if echo "$printer_options" | grep -q "Duplex"; then
            if echo "$printer_options" | grep "Duplex" | grep -q "DuplexNoTumble"; then
                print_options="-o Duplex=DuplexNoTumble"
                print_info "使用双面打印 (长边装订 - Duplex)"
            elif echo "$printer_options" | grep "Duplex" | grep -q "DuplexTumble"; then
                print_options="-o Duplex=DuplexTumble"
                print_info "使用双面打印 (短边装订 - Duplex)"
            else
                print_options="-o Duplex=DuplexNoTumble"
                print_info "使用默认双面打印设置 (Duplex)"
            fi
        # 备用：使用 sides 选项
        elif echo "$printer_options" | grep -q "sides"; then
            if echo "$printer_options" | grep "sides" | grep -q "two-sided-long-edge"; then
                print_options="-o sides=two-sided-long-edge"
                print_info "使用双面打印 (长边装订 - sides)"
            elif echo "$printer_options" | grep "sides" | grep -q "two-sided-short-edge"; then
                print_options="-o sides=two-sided-short-edge"
                print_info "使用双面打印 (短边装订 - sides)"
            else
                print_options="-o sides=two-sided-long-edge"
                print_info "使用默认双面打印设置 (sides)"
            fi
        fi
    else
        print_warning "打印机不支持双面打印，使用单面打印"
        # 尝试设置单面打印
        if echo "$printer_options" | grep -q "Duplex"; then
            print_options="-o Duplex=None"
        elif echo "$printer_options" | grep -q "sides"; then
            print_options="-o sides=one-sided"
        fi
    fi

    # 创建临时PDF文件
    local temp_pdf="/tmp/print_$(date +%s)_$(basename "$file" | sed 's/\.[^.]*$/.pdf/')"

    # 确保临时文件在脚本退出时被清理
    trap "rm -f '$temp_pdf'" EXIT

    # 转换为PDF
    print_info "正在转换 $filename 为PDF..."
    if ! libreoffice --headless --convert-to pdf --outdir /tmp "$file" 2>/dev/null; then
        print_error "✗ $filename 转换为PDF失败"
        return 1
    fi

    # 重命名生成的PDF文件
    local generated_pdf="/tmp/$(basename "$file" | sed 's/\.[^.]*$/.pdf/')"
    if [ -f "$generated_pdf" ]; then
        mv "$generated_pdf" "$temp_pdf"
    else
        print_error "✗ 未找到转换后的PDF文件"
        return 1
    fi

    # 打印PDF文件
    print_info "正在发送到打印机..."
    print_info "打印选项: $print_options"

    # 构建完整的打印命令
    local print_cmd="lp -d \"$PRINTER_NAME\""
    if [ -n "$print_options" ]; then
        print_cmd="$print_cmd $print_options"
    fi
    print_cmd="$print_cmd \"$temp_pdf\""

    # 执行打印命令
    if eval "$print_cmd" 2>/dev/null; then
        print_success "✓ $filename 已发送到打印队列"

        # 显示打印作业信息
        local job_id=$(lpq -P "$PRINTER_NAME" | tail -n 1 | awk '{print $1}')
        if [ -n "$job_id" ] && [[ "$job_id" =~ ^[0-9]+$ ]]; then
            print_info "打印作业ID: $job_id"
        fi

        # 记录成功状态
        log_print_status "SUCCESS" "$filename"

        # 清理临时文件
        rm -f "$temp_pdf"
        CURRENT_FILE=""
        return 0
    else
        print_error "✗ $filename 打印失败"
        print_error "打印命令: $print_cmd"

        # 尝试获取详细错误信息
        local error_output=$(eval "$print_cmd" 2>&1)
        if [ -n "$error_output" ]; then
            print_error "错误详情: $error_output"
        fi

        # 记录失败状态
        log_print_status "FAILED" "$filename"

        # 清理临时文件
        rm -f "$temp_pdf"
        CURRENT_FILE=""
        return 1
    fi
}

# 主函数
main() {
    # 解析命令行参数
    parse_arguments "$@"

    echo "========================================"
    echo "    Word文件批量双面打印工具"
    echo "========================================"
    echo

    # 处理特殊模式
    if [ -n "$RESUME_LOG_FILE" ]; then
        # 从指定日志文件恢复
        if [ ! -f "$RESUME_LOG_FILE" ]; then
            print_error "指定的日志文件不存在: $RESUME_LOG_FILE"
            exit 1
        fi

        # 从日志文件中提取目录和打印机信息
        local log_directory=$(grep "^# 目标目录:" "$RESUME_LOG_FILE" | cut -d' ' -f3)
        local log_printer=$(grep "^# 打印机:" "$RESUME_LOG_FILE" | cut -d' ' -f3)

        if [ -n "$log_directory" ]; then
            DIRECTORY="$log_directory"
        fi
        if [ -n "$log_printer" ] && [ -z "$PRINTER_NAME" ]; then
            PRINTER_NAME="$log_printer"
        fi

        print_info "从日志恢复模式"
        print_info "日志文件: $RESUME_LOG_FILE"
        print_info "目标目录: $DIRECTORY"

        # 系统检查
        check_libreoffice
        check_cups
        get_printer
        check_duplex_support

        # 使用现有日志文件
        PRINT_LOG_FILE="$RESUME_LOG_FILE"

        # 恢复打印
        resume_from_log "$RESUME_LOG_FILE"
        return $?

    elif [ "$RETRY_FAILED" = true ]; then
        # 重新打印失败的文件
        print_info "重新打印失败文件模式"
        print_info "目标目录: $DIRECTORY"

        local latest_log
        if latest_log=$(find_latest_log); then
            print_info "找到最新日志: $latest_log"

            # 系统检查
            check_libreoffice
            check_cups
            get_printer
            check_duplex_support

            # 使用现有日志文件
            PRINT_LOG_FILE="$latest_log"

            # 恢复打印
            resume_from_log "$latest_log"
            return $?
        else
            print_error "在目录 $DIRECTORY 中未找到打印日志文件"
            exit 1
        fi
    fi

    print_info "目标目录: $DIRECTORY"
    
    # 检查目录是否存在
    if [ ! -d "$DIRECTORY" ]; then
        print_error "目录 '$DIRECTORY' 不存在"
        exit 1
    fi
    
    # 系统检查
    check_libreoffice
    check_cups
    get_printer
    check_duplex_support

    # 初始化打印日志
    init_print_log

    echo
    
    # 查找Word文件
    local files
    mapfile -d $'\0' files < <(find "$DIRECTORY" -type f \( -name "*.doc" -o -name "*.docx" \) -print0)

    if [ ${#files[@]} -eq 0 ]; then
        print_warning "在目录 '$DIRECTORY' 中未找到Word文件"
        exit 0
    fi

    print_info "找到 ${#files[@]} 个Word文件"
    printf '%s\n' "${files[@]}" | while read -r file; do
        echo "  $(basename "$file")"
    done

    echo
    read -p "是否开始打印? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "取消打印"
        exit 0
    fi
    
    # 开始打印
    print_info "开始批量打印..."
    print_info "提示: 按 Ctrl+C 可以随时中断打印过程"
    echo

    local success_count=0
    local fail_count=0
    local skip_count=0
    local total_files=${#files[@]}
    local current_index=0

    for file in "${files[@]}"; do
        if [ -n "$file" ]; then  # 检查文件不为空
            ((current_index++))

            # 显示进度
            print_info "进度: $current_index/$total_files - $(basename "$file")"

            # 检查是否被中断
            if [ "$INTERRUPTED" = true ]; then
                print_warning "打印过程被中断"
                break
            fi

            # 打印文件
            local print_result
            if print_word_file "$file"; then
                print_result="success"
                ((success_count++))
            else
                print_result="failed"
                ((fail_count++))
            fi

            # 检查是否跳过
            if is_file_already_printed "$file" && [ "$print_result" != "success" ]; then
                ((skip_count++))
            fi

            # 稍作延迟，避免打印队列过载，同时允许用户中断
            if [ "$current_index" -lt "$total_files" ]; then
                print_info "等待 2 秒后继续下一个文件..."
                sleep 2
            fi
        fi
    done
    
    echo
    echo "========================================"
    if [ "$INTERRUPTED" = true ]; then
        print_warning "打印过程被用户中断！"
    else
        print_success "批量打印完成！"
    fi
    echo "========================================"

    # 显示打印摘要
    show_print_summary

    # 显示打印队列状态
    echo
    print_info "当前打印队列状态:"
    lpq -P "$PRINTER_NAME"
}

# 运行主函数
main "$@"
