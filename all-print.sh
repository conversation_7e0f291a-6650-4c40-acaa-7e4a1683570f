#!/bin/bash

# Word文件批量双面打印脚本
# 使用方法: ./print_word_files.sh [目录路径] [打印机名称]

# 配置部分
DIRECTORY="${1:-$(pwd)}"  # 使用参数指定目录，默认为当前目录
PRINTER_NAME="${2}"       # 打印机名称，如果未指定会自动检测

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色信息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 清理函数
cleanup() {
    # 清理所有临时PDF文件
    rm -f /tmp/print_*.pdf 2>/dev/null
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 检查LibreOffice是否安装
check_libreoffice() {
    if ! command -v libreoffice &> /dev/null; then
        print_error "LibreOffice未安装！"
        echo "请运行以下命令安装："
        echo "sudo pacman -S libreoffice-fresh"
        exit 1
    fi
    print_success "LibreOffice已安装"
}

# 检查CUPS服务
check_cups() {
    if ! systemctl is-active --quiet cups; then
        print_warning "CUPS服务未运行，正在启动..."
        sudo systemctl start cups
        if ! systemctl is-active --quiet cups; then
            print_error "无法启动CUPS服务"
            exit 1
        fi
    fi
    print_success "CUPS服务正常运行"
}

# 获取默认打印机或让用户选择
get_printer() {
    if [ -n "$PRINTER_NAME" ]; then
        # 检查指定的打印机是否存在
        if lpstat -p "$PRINTER_NAME" &>/dev/null; then
            print_success "使用指定打印机: $PRINTER_NAME"
            return 0
        else
            print_error "打印机 '$PRINTER_NAME' 不存在"
            exit 1
        fi
    fi
    
    # 尝试获取默认打印机
    DEFAULT_PRINTER=$(lpstat -d 2>/dev/null | grep "default destination" | awk '{print $NF}')
    
    if [ -n "$DEFAULT_PRINTER" ]; then
        PRINTER_NAME="$DEFAULT_PRINTER"
        print_success "使用默认打印机: $PRINTER_NAME"
        return 0
    fi
    
    # 列出所有可用打印机
    print_info "可用的打印机："
    lpstat -p | awk '{print "  " $2}'
    
    # 获取第一个可用打印机
    FIRST_PRINTER=$(lpstat -p | head -n1 | awk '{print $2}')
    if [ -n "$FIRST_PRINTER" ]; then
        PRINTER_NAME="$FIRST_PRINTER"
        print_warning "未设置默认打印机，使用: $PRINTER_NAME"
        read -p "是否继续? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_error "未找到可用的打印机"
        exit 1
    fi
}

# 检查打印机是否支持双面打印
check_duplex_support() {
    print_info "检查打印机双面打印支持..."

    # 获取打印机选项
    local printer_options=$(lpoptions -p "$PRINTER_NAME" -l 2>/dev/null)

    # 检查是否支持双面打印 (sides 或 Duplex)
    if echo "$printer_options" | grep -q -E "(sides|Duplex)"; then
        print_success "✓ 打印机支持双面打印"

        # 检查 sides 选项
        if echo "$printer_options" | grep -q "sides"; then
            local sides_options=$(echo "$printer_options" | grep "sides" | head -n1)
            print_info "支持的选项: $sides_options"

            if echo "$sides_options" | grep -q "two-sided-long-edge"; then
                print_info "  ✓ 支持长边装订双面打印 (sides)"
            fi
            if echo "$sides_options" | grep -q "two-sided-short-edge"; then
                print_info "  ✓ 支持短边装订双面打印 (sides)"
            fi
        fi

        # 检查 Duplex 选项
        if echo "$printer_options" | grep -q "Duplex"; then
            local duplex_options=$(echo "$printer_options" | grep "Duplex" | head -n1)
            print_info "支持的选项: $duplex_options"

            if echo "$duplex_options" | grep -q "DuplexNoTumble"; then
                print_info "  ✓ 支持长边装订双面打印 (Duplex)"
            fi
            if echo "$duplex_options" | grep -q "DuplexTumble"; then
                print_info "  ✓ 支持短边装订双面打印 (Duplex)"
            fi
        fi

        return 0
    else
        print_warning "⚠ 打印机不支持双面打印，将使用单面打印"

        # 显示所有可用选项供调试
        if [ -n "$printer_options" ]; then
            print_info "打印机支持的选项:"
            echo "$printer_options" | while read -r line; do
                if [ -n "$line" ]; then
                    echo "    $line"
                fi
            done
        fi

        return 1
    fi
}



# 打印Word文件
print_word_file() {
    local file="$1"
    local filename=$(basename "$file")

    print_info "正在打印: $filename"

    # 检查文件是否存在
    if [ ! -f "$file" ]; then
        print_error "文件不存在: $file"
        return 1
    fi

    # 检查打印机状态
    if ! lpstat -p "$PRINTER_NAME" | grep -q "enabled"; then
        print_error "打印机 $PRINTER_NAME 未启用"
        return 1
    fi

    # 检查打印机是否支持双面打印并设置选项
    local print_options=""
    local duplex_support=false
    local printer_options=$(lpoptions -p "$PRINTER_NAME" -l 2>/dev/null)

    # 检查打印机支持的双面打印选项
    if echo "$printer_options" | grep -q -E "(sides|Duplex)"; then
        duplex_support=true

        # 优先使用 Duplex 选项 (适用于商用打印机如SHARP)
        if echo "$printer_options" | grep -q "Duplex"; then
            if echo "$printer_options" | grep "Duplex" | grep -q "DuplexNoTumble"; then
                print_options="-o Duplex=DuplexNoTumble"
                print_info "使用双面打印 (长边装订 - Duplex)"
            elif echo "$printer_options" | grep "Duplex" | grep -q "DuplexTumble"; then
                print_options="-o Duplex=DuplexTumble"
                print_info "使用双面打印 (短边装订 - Duplex)"
            else
                print_options="-o Duplex=DuplexNoTumble"
                print_info "使用默认双面打印设置 (Duplex)"
            fi
        # 备用：使用 sides 选项
        elif echo "$printer_options" | grep -q "sides"; then
            if echo "$printer_options" | grep "sides" | grep -q "two-sided-long-edge"; then
                print_options="-o sides=two-sided-long-edge"
                print_info "使用双面打印 (长边装订 - sides)"
            elif echo "$printer_options" | grep "sides" | grep -q "two-sided-short-edge"; then
                print_options="-o sides=two-sided-short-edge"
                print_info "使用双面打印 (短边装订 - sides)"
            else
                print_options="-o sides=two-sided-long-edge"
                print_info "使用默认双面打印设置 (sides)"
            fi
        fi
    else
        print_warning "打印机不支持双面打印，使用单面打印"
        # 尝试设置单面打印
        if echo "$printer_options" | grep -q "Duplex"; then
            print_options="-o Duplex=None"
        elif echo "$printer_options" | grep -q "sides"; then
            print_options="-o sides=one-sided"
        fi
    fi

    # 创建临时PDF文件
    local temp_pdf="/tmp/print_$(date +%s)_$(basename "$file" | sed 's/\.[^.]*$/.pdf/')"

    # 确保临时文件在脚本退出时被清理
    trap "rm -f '$temp_pdf'" EXIT

    # 转换为PDF
    print_info "正在转换 $filename 为PDF..."
    if ! libreoffice --headless --convert-to pdf --outdir /tmp "$file" 2>/dev/null; then
        print_error "✗ $filename 转换为PDF失败"
        return 1
    fi

    # 重命名生成的PDF文件
    local generated_pdf="/tmp/$(basename "$file" | sed 's/\.[^.]*$/.pdf/')"
    if [ -f "$generated_pdf" ]; then
        mv "$generated_pdf" "$temp_pdf"
    else
        print_error "✗ 未找到转换后的PDF文件"
        return 1
    fi

    # 打印PDF文件
    print_info "正在发送到打印机..."
    print_info "打印选项: $print_options"

    # 构建完整的打印命令
    local print_cmd="lp -d \"$PRINTER_NAME\""
    if [ -n "$print_options" ]; then
        print_cmd="$print_cmd $print_options"
    fi
    print_cmd="$print_cmd \"$temp_pdf\""

    # 执行打印命令
    if eval "$print_cmd" 2>/dev/null; then
        print_success "✓ $filename 已发送到打印队列"

        # 显示打印作业信息
        local job_id=$(lpq -P "$PRINTER_NAME" | tail -n 1 | awk '{print $1}')
        if [ -n "$job_id" ] && [[ "$job_id" =~ ^[0-9]+$ ]]; then
            print_info "打印作业ID: $job_id"
        fi

        # 清理临时文件
        rm -f "$temp_pdf"
        return 0
    else
        print_error "✗ $filename 打印失败"
        print_error "打印命令: $print_cmd"

        # 尝试获取详细错误信息
        local error_output=$(eval "$print_cmd" 2>&1)
        if [ -n "$error_output" ]; then
            print_error "错误详情: $error_output"
        fi

        # 清理临时文件
        rm -f "$temp_pdf"
        return 1
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "    Word文件批量双面打印工具"
    echo "========================================"
    echo
    
    print_info "目标目录: $DIRECTORY"
    
    # 检查目录是否存在
    if [ ! -d "$DIRECTORY" ]; then
        print_error "目录 '$DIRECTORY' 不存在"
        exit 1
    fi
    
    # 系统检查
    check_libreoffice
    check_cups
    get_printer
    check_duplex_support
    
    echo
    
    # 查找Word文件
    local files
    mapfile -d $'\0' files < <(find "$DIRECTORY" -type f \( -name "*.doc" -o -name "*.docx" \) -print0)

    if [ ${#files[@]} -eq 0 ]; then
        print_warning "在目录 '$DIRECTORY' 中未找到Word文件"
        exit 0
    fi

    print_info "找到 ${#files[@]} 个Word文件"
    printf '%s\n' "${files[@]}" | while read -r file; do
        echo "  $(basename "$file")"
    done

    echo
    read -p "是否开始打印? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "取消打印"
        exit 0
    fi
    
    # 开始打印
    print_info "开始批量打印..."
    echo
    
    local success_count=0
    local fail_count=0
    
    for file in "${files[@]}"; do
        if [ -n "$file" ]; then  # 检查文件不为空
            if print_word_file "$file"; then
                ((success_count++))
            else
                ((fail_count++))
            fi
            sleep 2  # 稍作延迟，避免打印队列过载
        fi
    done
    
    echo
    echo "========================================"
    print_success "打印完成！"
    print_info "成功: $success_count 个文件"
    if [ $fail_count -gt 0 ]; then
        print_warning "失败: $fail_count 个文件"
    fi
    echo "========================================"
    
    # 显示打印队列状态
    echo
    print_info "当前打印队列状态:"
    lpq -P "$PRINTER_NAME"
}

# 运行主函数
main "$@"
