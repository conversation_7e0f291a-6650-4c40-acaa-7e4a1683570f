#!/bin/bash

# 双面打印测试脚本
# 用于测试打印机的双面打印功能

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

echo "========================================"
echo "    双面打印功能测试"
echo "========================================"
echo

# 检查CUPS服务
if ! systemctl is-active --quiet cups; then
    print_error "CUPS服务未运行"
    exit 1
fi

print_success "CUPS服务正常运行"

# 获取可用打印机
print_info "可用的打印机："
lpstat -p | while read -r line; do
    printer_name=$(echo "$line" | awk '{print $2}')
    if [ -n "$printer_name" ]; then
        echo "  $printer_name"
        
        # 检查双面打印支持 (sides 或 Duplex)
        printer_opts=$(lpoptions -p "$printer_name" -l 2>/dev/null)
        if echo "$printer_opts" | grep -q -E "(sides|Duplex)"; then
            echo "    ✓ 支持双面打印"

            # 显示支持的双面打印选项
            if echo "$printer_opts" | grep -q "sides"; then
                sides_info=$(echo "$printer_opts" | grep "sides" | head -n1)
                echo "    sides选项: $sides_info"
            fi
            if echo "$printer_opts" | grep -q "Duplex"; then
                duplex_info=$(echo "$printer_opts" | grep "Duplex" | head -n1)
                echo "    Duplex选项: $duplex_info"
            fi
        else
            echo "    ✗ 不支持双面打印"
        fi
        echo
    fi
done

# 获取默认打印机
DEFAULT_PRINTER=$(lpstat -d 2>/dev/null | grep "default destination" | awk '{print $NF}')

if [ -n "$DEFAULT_PRINTER" ]; then
    print_info "默认打印机: $DEFAULT_PRINTER"
    
    # 测试双面打印选项
    print_info "测试双面打印选项..."
    
    default_opts=$(lpoptions -p "$DEFAULT_PRINTER" -l 2>/dev/null)
    if echo "$default_opts" | grep -q -E "(sides|Duplex)"; then
        print_success "默认打印机支持双面打印"

        # 显示所有双面打印相关选项
        echo "双面打印选项详情:"
        echo "$default_opts" | grep -E "(sides|Duplex)" | while read -r option; do
            echo "  $option"
        done

        echo
        print_info "推荐的双面打印命令:"

        # 根据支持的选项给出建议
        if echo "$default_opts" | grep -q "Duplex"; then
            echo "  lp -d \"$DEFAULT_PRINTER\" -o Duplex=DuplexNoTumble 文件名.pdf  # 长边装订"
            echo "  lp -d \"$DEFAULT_PRINTER\" -o Duplex=DuplexTumble 文件名.pdf   # 短边装订"
        fi
        if echo "$default_opts" | grep -q "sides"; then
            echo "  lp -d \"$DEFAULT_PRINTER\" -o sides=two-sided-long-edge 文件名.pdf"
            echo "  lp -d \"$DEFAULT_PRINTER\" -o sides=two-sided-short-edge 文件名.pdf"
        fi

    else
        print_warning "默认打印机不支持双面打印"
    fi
else
    print_warning "未设置默认打印机"
fi

echo
echo "========================================"
print_info "测试完成"
echo "========================================"
