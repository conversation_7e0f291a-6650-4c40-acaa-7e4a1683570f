<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>双面打印测试文档</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2cm; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 1px solid #ccc; }
        .page-break { page-break-before: always; }
    </style>
</head>
<body>
    <h1>双面打印测试文档</h1>
    
    <h2>第一页内容</h2>
    <p>这是第一页的内容。这个文档用于测试双面打印功能。</p>
    <p>如果双面打印正常工作，这一页应该打印在纸张的正面。</p>
    
    <ul>
        <li>测试项目1：双面打印检测</li>
        <li>测试项目2：LibreOffice转换</li>
        <li>测试项目3：CUPS打印队列</li>
        <li>测试项目4：打印选项设置</li>
    </ul>
    
    <p>创建时间：$(date)</p>
    
    <div class="page-break"></div>
    
    <h2>第二页内容</h2>
    <p>这是第二页的内容。如果双面打印正常工作，这一页应该打印在纸张的背面。</p>
    
    <p><strong>双面打印测试要点：</strong></p>
    <ol>
        <li>检查打印机是否支持双面打印功能</li>
        <li>确认使用正确的打印选项（Duplex=DuplexNoTumble）</li>
        <li>验证LibreOffice能够正确转换Word文档</li>
        <li>确保CUPS打印系统正常工作</li>
    </ol>
    
    <p>如果您看到这个页面打印在纸张背面，说明双面打印功能正常工作！</p>
    
    <div class="page-break"></div>
    
    <h2>第三页内容</h2>
    <p>这是第三页的内容，用于进一步测试多页双面打印。</p>
    
    <p><em>技术细节：</em></p>
    <ul>
        <li>打印机型号：SHARP MX-3618N</li>
        <li>双面打印选项：Duplex=DuplexNoTumble（长边装订）</li>
        <li>转换工具：LibreOffice</li>
        <li>打印系统：CUPS</li>
    </ul>
    
    <p>测试完成时间：$(date)</p>
</body>
</html>
